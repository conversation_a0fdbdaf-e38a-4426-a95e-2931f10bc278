"""
Addon utilities

This package provides shared utility functions and classes for addon operations,
eliminating code duplication and providing centralized functionality.
"""

# Constants
from .constants import (
    AddonCategory,
    AddonState,
    DatabaseTables,
    DefaultValues,
    ErrorCodes,
    FileNames,
    HookPriorities,
    LogMessages,
    ManifestKeys,
    ModuleStates,
    ValidationLevels,
)

# Discovery utilities
from .discovery_utils import (
    build_dependency_graph,
    discover_addons_in_multiple_paths,
    discover_addons_in_path,
    find_missing_dependencies,
    get_addon_dependencies,
    get_installation_order,
    validate_addon_structure,
)

# Helper functions
from .helpers import (
    TimingContext,
    chunk_list,
    flatten_list,
    format_duration,
    merge_dictionaries,
    normalize_addon_name,
    retry_async_operation,
    safe_get_nested_value,
    validate_addon_name,
)

# Module registry utilities
from .module_registry_utils import (
    populate_ir_module_table_with_all_addons,
    read_manifest_file,
    unregister_addon_from_module_table,
    update_addon_state_in_module_table,
)

# Path resolver utilities
from .path_resolver import (
    AddonPathResolver,
    find_addon_path,
    get_addon_data_file_path,
    get_addon_manifest_path,
    get_addon_path_resolver,
    list_all_addons,
)

__all__ = [
    # Module registry utilities
    "populate_ir_module_table_with_all_addons",
    "unregister_addon_from_module_table",
    "update_addon_state_in_module_table",
    "read_manifest_file",
    # Discovery utilities
    "discover_addons_in_path",
    "discover_addons_in_multiple_paths",
    "validate_addon_structure",
    "get_addon_dependencies",
    "build_dependency_graph",
    "get_installation_order",
    "find_missing_dependencies",
    # Path resolver utilities
    "AddonPathResolver",
    "get_addon_path_resolver",
    "find_addon_path",
    "get_addon_manifest_path",
    "get_addon_data_file_path",
    "list_all_addons",
    # Helper functions
    "TimingContext",
    "retry_async_operation",
    "format_duration",
    "safe_get_nested_value",
    "merge_dictionaries",
    "validate_addon_name",
    "normalize_addon_name",
    "transaction_context",
    "chunk_list",
    "flatten_list",
    # Constants
    "AddonState",
    "AddonCategory",
    "ManifestKeys",
    "DefaultValues",
    "FileNames",
    "DatabaseTables",
    "ModuleStates",
    "HookPriorities",
    "ValidationLevels",
    "LogMessages",
    "ErrorCodes",
]
